using SmaTrendFollower.Models;

namespace SmaTrendFollower.Services;

/// <summary>
/// Interface for comprehensive system monitoring and alerting service
/// Provides real-time monitoring of all system components and performance metrics
/// </summary>
public interface ISystemMonitoringService : IDisposable
{
    // === Events ===
    
    /// <summary>
    /// Fired when a system alert is triggered
    /// </summary>
    event EventHandler<SystemAlertEventArgs>? AlertTriggered;
    
    /// <summary>
    /// Fired when system health status changes
    /// </summary>
    event EventHandler<SystemHealthChangeEventArgs>? HealthStatusChanged;
    
    /// <summary>
    /// Fired when performance metrics are updated
    /// </summary>
    event EventHandler<SystemMetricsUpdateEventArgs>? MetricsUpdated;
    
    // === Properties ===
    
    /// <summary>
    /// Current overall system health status
    /// </summary>
    SystemHealthStatus OverallHealth { get; }
    
    /// <summary>
    /// Number of active alerts
    /// </summary>
    int ActiveAlerts { get; }
    
    /// <summary>
    /// System uptime
    /// </summary>
    TimeSpan SystemUptime { get; }
    
    // === Monitoring Control ===
    
    /// <summary>
    /// Starts system monitoring
    /// </summary>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops system monitoring
    /// </summary>
    Task StopMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Performs immediate health check of all components
    /// </summary>
    Task<SystemHealthReport> PerformHealthCheckAsync(CancellationToken cancellationToken = default);
    
    // === Metrics and Reporting ===
    
    /// <summary>
    /// Gets current system performance metrics
    /// </summary>
    Task<SystemPerformanceMetrics> GetPerformanceMetricsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets component-specific health status
    /// </summary>
    Task<ComponentHealthStatus> GetComponentHealthAsync(string componentName, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets historical performance data
    /// </summary>
    Task<IEnumerable<SystemMetricsSnapshot>> GetHistoricalMetricsAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets active alerts
    /// </summary>
    Task<IEnumerable<SystemAlert>> GetActiveAlertsAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Gets alert history
    /// </summary>
    Task<IEnumerable<SystemAlert>> GetAlertHistoryAsync(DateTime startTime, DateTime endTime, CancellationToken cancellationToken = default);
    
    // === Configuration ===
    
    /// <summary>
    /// Configures monitoring thresholds and settings
    /// </summary>
    Task ConfigureMonitoringAsync(SystemMonitoringConfiguration configuration, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Adds custom metric monitoring
    /// </summary>
    Task AddCustomMetricAsync(string metricName, Func<Task<decimal>> metricProvider, MetricThresholds thresholds, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Removes custom metric monitoring
    /// </summary>
    Task RemoveCustomMetricAsync(string metricName, CancellationToken cancellationToken = default);
    
    // === Alerting ===
    
    /// <summary>
    /// Acknowledges an alert
    /// </summary>
    Task AcknowledgeAlertAsync(string alertId, string acknowledgedBy, string notes = "", CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Resolves an alert
    /// </summary>
    Task ResolveAlertAsync(string alertId, string resolvedBy, string resolution = "", CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Creates manual alert
    /// </summary>
    Task CreateManualAlertAsync(string title, string description, AlertSeverity severity, string source = "Manual", CancellationToken cancellationToken = default);
}

/// <summary>
/// System health status levels
/// </summary>
public enum SystemHealthStatus
{
    Healthy,
    Warning,
    Critical,
    Down,
    Unknown
}

/// <summary>
/// Alert severity levels
/// </summary>
public enum AlertSeverity
{
    Info,
    Warning,
    Error,
    Critical,
    Emergency
}

/// <summary>
/// System health report
/// </summary>
public readonly record struct SystemHealthReport(
    DateTime GeneratedAt,
    SystemHealthStatus OverallHealth,
    TimeSpan SystemUptime,
    List<ComponentHealthStatus> ComponentStatuses,
    List<SystemAlert> ActiveAlerts,
    SystemPerformanceMetrics PerformanceMetrics,
    string Summary
);

/// <summary>
/// Component health status
/// </summary>
public readonly record struct ComponentHealthStatus(
    string ComponentName,
    SystemHealthStatus Status,
    DateTime LastChecked,
    TimeSpan ResponseTime,
    string StatusMessage,
    Dictionary<string, object> Metrics,
    List<string> Issues
);

/// <summary>
/// System performance metrics
/// </summary>
public readonly record struct SystemPerformanceMetrics(
    DateTime Timestamp,
    double CpuUsagePercent,
    long MemoryUsageBytes,
    long MemoryAvailableBytes,
    double DiskUsagePercent,
    long NetworkBytesReceived,
    long NetworkBytesSent,
    int ActiveConnections,
    double RequestsPerSecond,
    double AverageResponseTime,
    int ErrorRate,
    Dictionary<string, decimal> CustomMetrics
);

/// <summary>
/// System metrics snapshot for historical data
/// </summary>
public readonly record struct SystemMetricsSnapshot(
    DateTime Timestamp,
    SystemPerformanceMetrics Metrics,
    SystemHealthStatus HealthStatus,
    int AlertCount
);

/// <summary>
/// System alert
/// </summary>
public readonly record struct SystemAlert(
    string Id,
    string Title,
    string Description,
    AlertSeverity Severity,
    string Source,
    DateTime CreatedAt,
    DateTime? AcknowledgedAt,
    string? AcknowledgedBy,
    DateTime? ResolvedAt,
    string? ResolvedBy,
    string? Resolution,
    Dictionary<string, object> Metadata
);

/// <summary>
/// System monitoring configuration
/// </summary>
public class SystemMonitoringConfiguration
{
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromMinutes(1);
    public TimeSpan MetricsCollectionInterval { get; set; } = TimeSpan.FromSeconds(30);
    public TimeSpan AlertEvaluationInterval { get; set; } = TimeSpan.FromSeconds(15);
    
    // Performance thresholds
    public double CpuWarningThreshold { get; set; } = 70.0;
    public double CpuCriticalThreshold { get; set; } = 90.0;
    public double MemoryWarningThreshold { get; set; } = 80.0;
    public double MemoryCriticalThreshold { get; set; } = 95.0;
    public double DiskWarningThreshold { get; set; } = 80.0;
    public double DiskCriticalThreshold { get; set; } = 95.0;
    
    // Response time thresholds
    public TimeSpan ResponseTimeWarningThreshold { get; set; } = TimeSpan.FromSeconds(5);
    public TimeSpan ResponseTimeCriticalThreshold { get; set; } = TimeSpan.FromSeconds(15);
    
    // Error rate thresholds
    public double ErrorRateWarningThreshold { get; set; } = 5.0; // 5%
    public double ErrorRateCriticalThreshold { get; set; } = 10.0; // 10%
    
    // Alert settings
    public bool EnableEmailAlerts { get; set; } = true;
    public bool EnableDiscordAlerts { get; set; } = true;
    public bool EnableSlackAlerts { get; set; } = false;
    public List<string> EmailRecipients { get; set; } = new();
    public string? DiscordWebhookUrl { get; set; }
    public string? SlackWebhookUrl { get; set; }
    
    // Data retention
    public TimeSpan MetricsRetentionPeriod { get; set; } = TimeSpan.FromDays(30);
    public TimeSpan AlertRetentionPeriod { get; set; } = TimeSpan.FromDays(90);
    
    // Component monitoring
    public List<string> MonitoredComponents { get; set; } = new()
    {
        "TradingService",
        "MarketDataService",
        "PolygonWebSocketClient",
        "AlpacaWebSocketClient",
        "RedisConnectionService",
        "DatabaseService",
        "DiscordNotificationService"
    };
    
    // Custom metrics
    public Dictionary<string, MetricThresholds> CustomMetricThresholds { get; set; } = new();
}

/// <summary>
/// Metric thresholds for custom metrics
/// </summary>
public class MetricThresholds
{
    public decimal? WarningMin { get; set; }
    public decimal? WarningMax { get; set; }
    public decimal? CriticalMin { get; set; }
    public decimal? CriticalMax { get; set; }
    public string? Unit { get; set; }
    public string? Description { get; set; }
}

/// <summary>
/// Event arguments for system alerts
/// </summary>
public class SystemAlertEventArgs : EventArgs
{
    public required SystemAlert Alert { get; init; }
    public required DateTime TriggeredAt { get; init; }
}

/// <summary>
/// Event arguments for system health changes
/// </summary>
public class SystemHealthChangeEventArgs : EventArgs
{
    public required SystemHealthStatus PreviousStatus { get; init; }
    public required SystemHealthStatus CurrentStatus { get; init; }
    public required DateTime ChangeTime { get; init; }
    public required string Reason { get; init; }
}

/// <summary>
/// Event arguments for system metrics updates
/// </summary>
public class SystemMetricsUpdateEventArgs : EventArgs
{
    public required SystemPerformanceMetrics Metrics { get; init; }
    public required DateTime UpdateTime { get; init; }
}
