using Alpaca.Markets;

namespace SmaTrendFollower.Models;

/// <summary>
/// Represents a bar from Polygon API with proper timezone handling.
/// TimeUtc is converted from Polygon's milliseconds-since-epoch format to DateTime UTC
/// to ensure consistency with Alpaca bar timestamps when mixing data sources.
/// </summary>
public readonly record struct PolygonBar(
    string Symbol,
    DateTime TimeUtc, // Always UTC, converted from Polygon's milliseconds since epoch
    decimal Open,
    decimal High,
    decimal Low,
    decimal Close,
    long Volume
);

/// <summary>
/// Wrapper class that implements IBar interface for Polygon data compatibility.
/// Ensures Polygon bars can be used interchangeably with Alpaca bars by implementing
/// the same IBar interface with consistent UTC timezone handling.
/// </summary>
public class PolygonBarWrapper : IBar
{
    private readonly PolygonBar _polygonBar;

    public PolygonBarWrapper(PolygonBar polygonBar)
    {
        _polygonBar = polygonBar;
    }

    public string Symbol => _polygonBar.Symbol;
    public DateTime TimeUtc => _polygonBar.TimeUtc;
    public decimal Open => _polygonBar.Open;
    public decimal High => _polygonBar.High;
    public decimal Low => _polygonBar.Low;
    public decimal Close => _polygonBar.Close;
    public decimal Volume => _polygonBar.Volume;
    public decimal Vwap => 0; // Not available from Polygon aggregates
    public ulong TradeCount => 0; // Not available from Polygon aggregates
}

/// <summary>
/// Implements IPage<IBar> for Polygon bar data to maintain compatibility with Alpaca interfaces
/// </summary>
public class PolygonBarPage : IPage<IBar>
{
    private readonly List<PolygonBar> _bars;

    public PolygonBarPage(List<PolygonBar> bars, string symbol)
    {
        _bars = bars ?? new List<PolygonBar>();
        Symbol = symbol;
    }

    public string Symbol { get; }
    public IReadOnlyList<IBar> Items => _bars.Select(b => new PolygonBarWrapper(b)).Cast<IBar>().ToList();
    public string? NextPageToken => null; // Polygon fallback doesn't support pagination in this implementation
}

/// <summary>
/// Polygon aggregates API response
/// </summary>
public class PolygonAggregatesResponse
{
    public string? Status { get; set; }
    public int ResultsCount { get; set; }
    public List<PolygonAggregateResult>? Results { get; set; }
}

/// <summary>
/// Individual aggregate result from Polygon API
/// </summary>
public class PolygonAggregateResult
{
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public long Volume { get; set; }
    public decimal Vwap { get; set; }
    public long Timestamp { get; set; }
    public long Transactions { get; set; }
}

/// <summary>
/// Polygon open/close API response
/// </summary>
public class PolygonOpenCloseResponse
{
    public string? Status { get; set; }
    public string? Symbol { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public long Volume { get; set; }
    public string? From { get; set; }
}

/// <summary>
/// Polygon trades API response
/// </summary>
public class PolygonTradesResponse
{
    public string? Status { get; set; }
    public int ResultsCount { get; set; }
    public List<PolygonTradeResult>? Results { get; set; }
}

/// <summary>
/// Individual trade result from Polygon trades API
/// </summary>
public class PolygonTradeResult
{
    public decimal Price { get; set; }
    public long Size { get; set; }
    public long ParticipantTimestamp { get; set; }
    public string? Exchange { get; set; }
    public List<int>? Conditions { get; set; }
}

/// <summary>
/// Implements IPage<IBar> for cached bar data to maintain compatibility with Alpaca interfaces
/// </summary>
public class CachedBarPage : IPage<IBar>
{
    private readonly List<IBar> _bars;

    public CachedBarPage(List<IBar> bars, string symbol)
    {
        _bars = bars ?? new List<IBar>();
        Symbol = symbol;
    }

    public string Symbol { get; }
    public IReadOnlyList<IBar> Items => _bars;
    public string? NextPageToken => null; // Cached data doesn't support pagination
}
